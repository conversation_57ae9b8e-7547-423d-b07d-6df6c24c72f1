"use client";

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface Animated3DElementsProps {
  variant?: 'hero' | 'section' | 'minimal';
  className?: string;
}

export function Animated3DElements({ variant = 'hero', className = '' }: Animated3DElementsProps) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: (e.clientY / window.innerHeight) * 2 - 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (variant === 'hero') {
    return (
      <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
        {/* Large Floating Geometric Shapes */}
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-primary/10 shape-3d animate-float3d"
          style={{
            borderRadius: '30% 70% 70% 30% / 30% 30% 70% 70%',
          }}
          animate={{
            x: mousePosition.x * 20,
            y: mousePosition.y * 20,
            rotateX: mousePosition.y * 10,
            rotateY: mousePosition.x * 10,
          }}
          transition={{ type: "spring", stiffness: 50, damping: 20 }}
        />

        <motion.div
          className="absolute top-40 right-20 w-24 h-24 bg-primary/20 shape-3d animate-rotate3d"
          style={{
            borderRadius: '50%',
            background: 'linear-gradient(45deg, rgba(0, 175, 240, 0.2), rgba(0, 175, 240, 0.05))',
          }}
          animate={{
            x: mousePosition.x * -15,
            y: mousePosition.y * -15,
          }}
          transition={{ type: "spring", stiffness: 30, damping: 15 }}
        />

        <motion.div
          className="absolute bottom-40 left-20 w-20 h-20 bg-primary/15 shape-3d animate-morph"
          animate={{
            x: mousePosition.x * 25,
            y: mousePosition.y * 25,
            scale: 1 + mousePosition.x * 0.1,
          }}
          transition={{ type: "spring", stiffness: 40, damping: 25 }}
        />

        <motion.div
          className="absolute bottom-20 right-10 w-28 h-28 bg-primary/8 shape-3d animate-perspective-tilt"
          style={{
            borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%',
          }}
          animate={{
            x: mousePosition.x * -20,
            y: mousePosition.y * -20,
            rotateZ: mousePosition.x * 5,
          }}
          transition={{ type: "spring", stiffness: 35, damping: 18 }}
        />

        {/* Orbiting Elements */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <motion.div
            className="w-4 h-4 bg-primary/30 rounded-full animate-orbit"
            style={{
              transformOrigin: '0 0',
            }}
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </div>

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-primary/20 rounded-full"
            style={{
              top: `${20 + (i * 10)}%`,
              left: `${10 + (i * 8)}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.3,
            }}
          />
        ))}

        {/* 3D Grid Background */}
        <div className="absolute inset-0 opacity-5">
          <div 
            className="w-full h-full"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0, 175, 240, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 175, 240, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px',
              transform: 'perspective(1000px) rotateX(60deg)',
              transformOrigin: 'center bottom',
            }}
          />
        </div>
      </div>
    );
  }

  if (variant === 'section') {
    return (
      <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
        {/* Medium Floating Elements */}
        <motion.div
          className="absolute top-10 right-10 w-16 h-16 bg-primary/10 shape-3d animate-float3d"
          style={{
            borderRadius: '40% 60% 60% 40% / 40% 40% 60% 60%',
          }}
          animate={{
            x: mousePosition.x * 10,
            y: mousePosition.y * 10,
          }}
          transition={{ type: "spring", stiffness: 60, damping: 25 }}
        />

        <motion.div
          className="absolute bottom-10 left-10 w-12 h-12 bg-primary/15 rounded-full animate-rotate3d"
          animate={{
            x: mousePosition.x * -8,
            y: mousePosition.y * -8,
          }}
          transition={{ type: "spring", stiffness: 50, damping: 20 }}
        />

        {/* Subtle Particles */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-primary/25 rounded-full"
            style={{
              top: `${30 + (i * 15)}%`,
              right: `${15 + (i * 10)}%`,
            }}
            animate={{
              y: [0, -15, 0],
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.4,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    );
  }

  // Minimal variant
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      <motion.div
        className="absolute top-5 right-5 w-8 h-8 bg-primary/10 rounded-full animate-float3d"
        animate={{
          x: mousePosition.x * 5,
          y: mousePosition.y * 5,
        }}
        transition={{ type: "spring", stiffness: 70, damping: 30 }}
      />
      
      <motion.div
        className="absolute bottom-5 left-5 w-6 h-6 bg-primary/15 shape-3d"
        style={{
          borderRadius: '30% 70% 70% 30% / 30% 30% 70% 70%',
        }}
        animate={{
          x: mousePosition.x * -3,
          y: mousePosition.y * -3,
          rotate: mousePosition.x * 10,
        }}
        transition={{ type: "spring", stiffness: 80, damping: 35 }}
      />
    </div>
  );
}

// 3D Interactive Card Component
interface Interactive3DCardProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
}

export function Interactive3DCard({ children, className = '', intensity = 1 }: Interactive3DCardProps) {
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const rotateXValue = ((e.clientY - centerY) / rect.height) * -20 * intensity;
    const rotateYValue = ((e.clientX - centerX) / rect.width) * 20 * intensity;
    
    setRotateX(rotateXValue);
    setRotateY(rotateYValue);
  };

  const handleMouseLeave = () => {
    setRotateX(0);
    setRotateY(0);
    setIsHovered(false);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  return (
    <motion.div
      className={`card-3d ${className}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      animate={{
        rotateX,
        rotateY,
        scale: isHovered ? 1.02 : 1,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
      style={{
        transformStyle: "preserve-3d",
        perspective: "1000px",
      }}
    >
      {children}
    </motion.div>
  );
}
