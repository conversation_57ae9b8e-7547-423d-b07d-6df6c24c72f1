"use client";

import { motion } from 'framer-motion';
import { 
  Users, 
  TrendingUp, 
  Star, 
  Zap, 
  Target, 
  Sparkles,
  Heart,
  MessageCircle,
  Share2,
  Crown
} from 'lucide-react';

interface Floating3DIconProps {
  icon: React.ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function Floating3DIcon({ 
  icon, 
  delay = 0, 
  duration = 6, 
  className = '',
  size = 'md'
}: Floating3DIconProps) {
  const sizeClasses = {
    sm: 'w-8 h-8 p-2',
    md: 'w-12 h-12 p-3',
    lg: 'w-16 h-16 p-4'
  };

  return (
    <motion.div
      className={`
        ${sizeClasses[size]} 
        bg-white/80 backdrop-blur-lg rounded-xl shadow-lg border border-primary/20
        flex items-center justify-center text-primary
        ${className}
      `}
      animate={{
        y: [0, -20, 0],
        rotateX: [0, 10, 0],
        rotateY: [0, 15, 0],
        scale: [1, 1.05, 1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        delay,
        ease: "easeInOut",
      }}
      style={{
        transformStyle: "preserve-3d",
      }}
      whileHover={{
        scale: 1.1,
        rotateY: 25,
        transition: { duration: 0.3 }
      }}
    >
      {icon}
    </motion.div>
  );
}

// Predefined floating icon sets
export function FloatingIconsSet({ variant = 'hero' }: { variant?: 'hero' | 'features' | 'stats' }) {
  if (variant === 'hero') {
    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <Floating3DIcon
          icon={<Users className="w-full h-full" />}
          className="absolute top-20 left-[10%]"
          delay={0}
          duration={8}
          size="lg"
        />
        
        <Floating3DIcon
          icon={<TrendingUp className="w-full h-full" />}
          className="absolute top-32 right-[15%]"
          delay={1}
          duration={7}
          size="md"
        />
        
        <Floating3DIcon
          icon={<Star className="w-full h-full" />}
          className="absolute top-[60%] left-[5%]"
          delay={2}
          duration={9}
          size="md"
        />
        
        <Floating3DIcon
          icon={<Zap className="w-full h-full" />}
          className="absolute bottom-32 right-[10%]"
          delay={0.5}
          duration={6}
          size="lg"
        />
        
        <Floating3DIcon
          icon={<Crown className="w-full h-full" />}
          className="absolute top-[45%] right-[8%]"
          delay={1.5}
          duration={8}
          size="sm"
        />
        
        <Floating3DIcon
          icon={<Sparkles className="w-full h-full" />}
          className="absolute bottom-[20%] left-[12%]"
          delay={2.5}
          duration={7}
          size="sm"
        />
      </div>
    );
  }

  if (variant === 'features') {
    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <Floating3DIcon
          icon={<Target className="w-full h-full" />}
          className="absolute top-10 right-10"
          delay={0}
          duration={6}
          size="md"
        />
        
        <Floating3DIcon
          icon={<MessageCircle className="w-full h-full" />}
          className="absolute bottom-10 left-10"
          delay={1}
          duration={7}
          size="sm"
        />
        
        <Floating3DIcon
          icon={<Share2 className="w-full h-full" />}
          className="absolute top-[50%] right-5"
          delay={2}
          duration={8}
          size="sm"
        />
      </div>
    );
  }

  // Stats variant
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <Floating3DIcon
        icon={<Heart className="w-full h-full" />}
        className="absolute top-5 left-5"
        delay={0}
        duration={5}
        size="sm"
      />
      
      <Floating3DIcon
        icon={<TrendingUp className="w-full h-full" />}
        className="absolute bottom-5 right-5"
        delay={1}
        duration={6}
        size="sm"
      />
    </div>
  );
}

// 3D Animated Background Grid
export function Animated3DGrid({ className = '' }: { className?: string }) {
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      <motion.div
        className="absolute inset-0 opacity-[0.02]"
        animate={{
          rotateX: [60, 65, 60],
          rotateY: [0, 5, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 175, 240, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 175, 240, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px',
          transform: 'perspective(1000px) rotateX(60deg)',
          transformOrigin: 'center bottom',
          transformStyle: 'preserve-3d',
        }}
      />
    </div>
  );
}

// 3D Morphing Blob
export function Morphing3DBlob({ 
  className = '',
  size = 'md',
  color = 'primary'
}: { 
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary';
}) {
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  };

  const colorClasses = {
    primary: 'bg-primary/10',
    secondary: 'bg-blue-300/20'
  };

  return (
    <motion.div
      className={`
        ${sizeClasses[size]} 
        ${colorClasses[color]}
        ${className}
        shape-3d
      `}
      animate={{
        borderRadius: [
          "60% 40% 30% 70% / 60% 30% 70% 40%",
          "30% 60% 70% 40% / 50% 60% 30% 60%",
          "40% 60% 60% 40% / 60% 30% 60% 70%",
          "60% 40% 30% 70% / 60% 30% 70% 40%"
        ],
        rotate: [0, 90, 180, 270, 360],
        scale: [1, 1.1, 0.9, 1.05, 1],
      }}
      transition={{
        duration: 15,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      style={{
        transformStyle: "preserve-3d",
      }}
    />
  );
}

// 3D Particle System
export function Particle3DSystem({ 
  particleCount = 12,
  className = ''
}: { 
  particleCount?: number;
  className?: string;
}) {
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {[...Array(particleCount)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-primary/30 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.random() * 20 - 10, 0],
            opacity: [0, 1, 0],
            scale: [0, 1.5, 0],
            rotateZ: [0, 360],
          }}
          transition={{
            duration: 4 + Math.random() * 4,
            repeat: Infinity,
            delay: i * 0.3,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
