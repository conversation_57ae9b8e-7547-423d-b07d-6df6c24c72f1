"use client";

import { useEffect, useState, useMemo, useRef } from "react";
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';

export function Stats() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [animatedActiveCreators, setAnimatedActiveCreators] = useState(0);
  const [startCounterAnimation, setStartCounterAnimation] = useState(false);
  const statsSectionRef = useRef<HTMLDivElement>(null);
  
  const statsData = useMemo(() => [
    {
      value: "1,000+",
      targetValue: 1000, // For animation
      label: "Active Creators",
      description: "Trust our platform daily",
      colorClass: "group-hover:text-primary", // Default, will be overridden for specific items
      isAnimated: true
    },
    {
      value: "$2M+",
      label: "Revenue Recovered",
      description: "For our users this year",
      colorClass: "text-green-400 group-hover:text-green-300"
    },
    {
      value: "4.7★",
      label: "User Rating",
      description: "From Chrome Web Store",
      colorClass: "text-yellow-400 group-hover:text-yellow-300"
    },
    {
      value: "99.9%",
      label: "Uptime",
      description: "Reliable service 24/7",
      colorClass: "group-hover:text-primary"
    }
  ], []);

  const testimonials = useMemo(() => [
    {
      name: "Sarah M.",
      role: "Top 1% Creator",
      avatar: "/images/avatar1.jpg",
      rating: 5,
      text: "ofautofollower.com helped me recover over $15,000 in lost revenue from expired fans. It's an absolute game-changer for any serious creator."
    },
    {
      name: "Jessica L.",
      role: "Top 0.01% Creator",
      avatar: "/images/avatar2.jpg",
      rating: 5,
      text: "I doubled my monthly earnings within 2 months of using ofautofollower.com. The priority messaging feature is incredible - my open rates went from 20% to 85%!"
    },
    {
      name: "Damien",
      role: "Agency Owner",
      avatar: "/images/avatar3.jpg",
      rating: 5,
      text: "ofautofollower.com has been an absolutely indispensable tool in our Arsenal as agency owners. Thank you for a great product."
    }
  ], []);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Auto-rotate testimonials every 3 seconds, pausing on hover
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isHovering) {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }
    }, 3000);
    return () => clearInterval(interval);
  }, [testimonials.length, isHovering]);

  // Intersection Observer for the stats section
  useEffect(() => {
    const currentRef = statsSectionRef.current;
    
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && !startCounterAnimation) {
          setStartCounterAnimation(true);
          if (currentRef) {
            observer.unobserve(currentRef); // Unobserve after triggering
          }
        }
      },
      { threshold: 0.1 } // Trigger when 10% of the element is visible
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [startCounterAnimation]); // Re-run if startCounterAnimation changes (though unobserve should prevent re-trigger)

  // Animate Active Creators count when startCounterAnimation is true
  useEffect(() => {
    if (!startCounterAnimation) return; // Don't run animation if not visible yet

    const item = statsData.find(s => s.label === "Active Creators");
    if (item && item.targetValue) {
      const target = item.targetValue;
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          setAnimatedActiveCreators(target);
          clearInterval(timer);
        } else {
          setAnimatedActiveCreators(Math.floor(current));
        }
      }, 16);
      return () => clearInterval(timer);
    }
  }, [statsData, startCounterAnimation]); // Depend on startCounterAnimation

  return (
    <section ref={statsSectionRef} className="py-20 section-bg">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by <span className="gradient-text">the best creators</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Join thousands of successful OnlyFans creators who rely on our platform to maximize their earnings.
          </p>
        </div>
        
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {statsData.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-white rounded-2xl p-8 border border-gray-200 hover:border-primary/40 hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                <div className={`text-2xl md:text-5xl font-bold mb-2 transition-colors ${stat.isAnimated ? 'text-gray-900' : stat.colorClass ? stat.colorClass : 'text-gray-900 group-hover:text-primary'}`}>
                  {stat.isAnimated ? `${animatedActiveCreators.toLocaleString()}+` : stat.value}
                </div>
                <div className="text-sm md:text-lg font-semibold text-gray-700 mb-1">
                  {stat.label}
                </div>
                <div className="text-xs md:text-sm text-gray-500">
                  {stat.description}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials Carousel */}
        <div className="mt-16">
          <div
            className="bg-white rounded-2xl p-8 max-w-4xl mx-auto relative border border-gray-200"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            onTouchStart={() => setIsHovering(true)}
            onTouchEnd={() => setIsHovering(false)}
          >
            {/* Navigation buttons */}
            <button
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              aria-label="Next testimonial"
            >
              <ChevronRight className="w-5 h-5 text-gray-600" />
            </button>

            {/* Testimonial content */}
            <div className="text-center px-8">
              {/* Avatar */}
              <div className="mb-4">
                <Image
                  src={testimonials[currentTestimonial].avatar}
                  alt={`${testimonials[currentTestimonial].name} - ${testimonials[currentTestimonial].role}`}
                  width={80}
                  height={80}
                  className="w-20 h-20 rounded-full mx-auto object-cover border-4 border-gray-200"
                  loading="lazy"
                />
              </div>

              {/* Author info */}
              <div className="text-gray-600 mb-4">
                <div className="font-semibold text-lg">{testimonials[currentTestimonial].name}</div>
                <div className="text-sm">{testimonials[currentTestimonial].role}</div>
              </div>

              {/* Rating stars */}
              <div className="flex justify-center mb-4">
                <div className="flex text-yellow-400" role="img" aria-label={`${testimonials[currentTestimonial].rating} out of 5 stars`}>
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20" aria-hidden="true">
                      <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                    </svg>
                  ))}
                </div>
              </div>

              {/* Testimonial text */}
              <blockquote className="text-xl text-gray-700 mb-6 font-medium italic max-w-3xl mx-auto">
                &ldquo;{testimonials[currentTestimonial].text}&rdquo;
              </blockquote>
            </div>

            {/* Carousel indicators */}
            <div className="flex justify-center gap-2 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 ${
                    index === currentTestimonial 
                      ? 'bg-white w-8' 
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 